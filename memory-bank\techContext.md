# Technical Context: Ultimate Electrical Designer

## 1. Technology Stack

This document outlines the specific technologies and frameworks that form the technical foundation of the Ultimate Electrical Designer platform.

### 1.1. Backend (Python Service)
- **Language**: Python 3.13+
- **Framework**: FastAPI (0.115+) for building high-performance APIs.
- **Web Server**: <PERSON>vicorn serves as the ASGI server for FastAPI.
- **Database ORM**: SQLAlchemy (2.0+) for database interaction, supporting PostgreSQL and SQLite.
- **Database Migrations**: Alembic for managing database schema changes.
- **Data Validation**: Pydantic for data validation, serialization, and settings management.
- **Authentication**: JSON Web Tokens (JWT) implemented with `python-jose` and `passlib`.
- **Scientific Computing**: NumPy, SciPy, and Pandas for complex engineering calculations.
- **Dependency Management**: Poetry for managing Python packages and virtual environments.

### 1.2. Frontend (Next.js Client)
- **Framework**: Next.js (15.3+) with the App Router.
- **Language**: TypeScript (5.8+) for complete type safety.
- **Styling**: Tailwind CSS (4.1+) for utility-first CSS.
- **State Management**:
    - **React Query (TanStack Query)**: For managing server state (fetching, caching, synchronizing).
    - **Zustand**: For minimal, fast, and scalable client state management.
- **Package Management**: npm or yarn.

### 1.3. Integration Services (C#)
- **Framework**: .NET 8.0+ with ASP.NET Core for building web APIs.
- **CAD Integration**: The `cad-integrator-service` uses the AutoCAD .NET API (ObjectARX) for deep integration with AutoCAD.
- **Inter-service Communication**: Services communicate via REST APIs or gRPC for high-performance scenarios.

## 2. Development Environment & Tooling

- **Containerization**: Docker is used for creating reproducible development and production environments for all services. Docker Compose is planned for multi-service orchestration.
- **Code Quality & Linting**:
    - **Python**: `Ruff` for linting and formatting, enforcing a strict, consistent code style.
    - **TypeScript**: `ESLint` for identifying and fixing problems in JavaScript/TypeScript code.
- **Type Checking**:
    - **Python**: `MyPy` is used for static type checking, with a policy of 100% type annotation coverage.
    - **TypeScript**: The TypeScript compiler (`tsc`) ensures full type safety in the frontend.
- **Testing**:
    - **Backend**: `Pytest` is the framework for all backend tests (unit, integration, performance).
    - **Frontend**: `Vitest` for unit tests, `React Testing Library` for component testing, and `Playwright` for end-to-end testing.

## 3. Technical Constraints & Decisions

- **Zero-Tolerance Quality Gates**: The project enforces a "zero-tolerance" policy for linting errors, type-checking failures, and failing tests. No code that violates these checks should be merged.
- **Standards Compliance**: All engineering calculations must be verifiable against the specified IEC, EN, and IEEE standards. This is a hard constraint that influences the design of calculation logic.
- **Database Choice**: While SQLite is acceptable for local development, the production environment must use PostgreSQL for its robustness and feature set.
- **API Documentation**: The FastAPI backend automatically generates OpenAPI (Swagger) and ReDoc documentation. All public API endpoints must be fully documented using FastAPI's docstring and Pydantic model integration.
