# tests/api/test_user_routes.py
"""Tests for user management API endpoints."""

from fastapi.testclient import Test<PERSON><PERSON>
from src.core.enums import UserRole


class TestUserRoutes:
    """Test suite for user management endpoints."""

    def test_get_current_user_profile(self, authenticated_client: TestClient, test_user):
        """Test getting current user's profile."""
        response = authenticated_client.get("/api/v1/users/me")

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_user.id
        assert data["name"] == test_user.name
        assert data["email"] == test_user.email
        assert data["role"] == test_user.role
        assert "password_hash" not in data

    def test_get_current_user_profile_unauthenticated(self, client: TestClient):
        """Test getting current user profile without authentication."""
        response = client.get("/api/v1/users/me")

        assert response.status_code == 401

    def test_update_current_user_profile(self, authenticated_client: TestClient):
        """Test updating current user's profile."""
        update_data = {"name": "Updated User Name"}

        response = authenticated_client.put("/api/v1/users/me", json=update_data)

        assert response.status_code == 200
        data = response.json()

        assert data["name"] == "Updated User Name"

    def test_update_current_user_profile_forbidden_fields(
        self, authenticated_client: TestClient
    ):
        """Test updating forbidden fields in current user's profile."""
        update_data = {
            "role": UserRole.ADMIN.value  # Users shouldn't be able to change their role
        }

        response = authenticated_client.put("/api/v1/users/me", json=update_data)

        assert response.status_code == 422

    def test_get_users_summary_admin(self, admin_client: TestClient, test_user):
        """Test getting users summary as admin."""
        response = admin_client.get("/api/v1/users/summary")

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        if data:
            user_summary = data[0]
            required_fields = ["id", "name", "email", "role", "is_active"]
            for field in required_fields:
                assert field in user_summary

    def test_get_users_summary_non_admin(self, authenticated_client: TestClient):
        """Test getting users summary as non-admin."""
        response = authenticated_client.get("/api/v1/users/summary")

        assert response.status_code == 403

    def test_get_users_summary_with_limit(self, admin_client: TestClient):
        """Test getting users summary with limit parameter."""
        response = admin_client.get("/api/v1/users/summary?limit=5")

        assert response.status_code == 200
        data = response.json()

        assert isinstance(data, list)
        assert len(data) <= 5

    def test_get_user_profile_by_id_admin(
        self, admin_client: TestClient, test_user
    ):
        """Test getting user profile by ID as admin."""
        response = admin_client.get(f"/api/v1/users/{test_user.id}/profile")

        assert response.status_code == 200
        data = response.json()

        assert data["id"] == test_user.id
        assert data["email"] == test_user.email

    def test_get_user_profile_by_id_non_admin(
        self, authenticated_client: TestClient, test_user
    ):
        """Test getting user profile by ID as non-admin."""
        response = authenticated_client.get(f"/api/v1/users/{test_user.id}/profile")

        assert response.status_code == 403

    def test_get_user_profile_by_id_not_found(self, admin_client: TestClient):
        """Test getting user profile for non-existent user."""
        response = admin_client.get("/api/v1/users/99999/profile")

        assert response.status_code == 404

    def test_activate_user_account_admin(
        self, admin_client: TestClient, test_user
    ):
        """Test activating user account as admin."""
        # First deactivate the user
        admin_client.post(f"/api/v1/users/{test_user.id}/deactivate")

        # Then activate
        response = admin_client.post(
            f"/api/v1/users/{test_user.id}/activate"
        )

        assert response.status_code == 200
        data = response.json()

        assert data["is_active"] is True

    def test_activate_user_account_non_admin(
        self, authenticated_client: TestClient, test_user
    ):
        """Test activating user account as non-admin."""
        response = authenticated_client.post(
            f"/api/v1/users/{test_user.id}/activate"
        )

        assert response.status_code == 403

    def test_deactivate_user_account_admin(
        self, admin_client: TestClient, test_user
    ):
        """Test deactivating user account as admin."""
        response = admin_client.post(
            f"/api/v1/users/{test_user.id}/deactivate"
        )

        assert response.status_code == 200
        data = response.json()

        assert data["is_active"] is False

    def test_deactivate_own_account_admin(
        self, admin_client: TestClient, test_admin_user
    ):
        """Test admin trying to deactivate their own account."""
        response = admin_client.post(
            f"/api/v1/users/{test_admin_user.id}/deactivate"
        )

        assert response.status_code == 400
        data = response.json()
        assert "Cannot deactivate your own account" in data["detail"]

    def test_deactivate_user_account_non_admin(
        self, authenticated_client: TestClient, test_user
    ):
        """Test deactivating user account as non-admin."""
        response = authenticated_client.post(
            f"/api/v1/users/{test_user.id}/deactivate"
        )

        assert response.status_code == 403

    def test_user_crud_operations_admin(self, admin_client: TestClient):
        """Test CRUD operations for users as admin."""
        # Create user
        user_data = {
            "name": "New Test User",
            "email": "<EMAIL>",
            "password": "NewUserPass123",
            "role": UserRole.VIEWER.value,
            "is_active": True,
        }

        create_response = admin_client.post("/api/v1/users/", json=user_data)
        assert create_response.status_code == 201
        test_user = create_response.json()
        user_id = test_user["id"]

        # Read user
        read_response = admin_client.get(f"/api/v1/users/{user_id}")
        assert read_response.status_code == 200

        # Update user
        update_data = {"name": "Updated Test User"}
        update_response = admin_client.put(
            f"/api/v1/users/{user_id}", json=update_data
        )
        assert update_response.status_code == 200

        # Delete user
        delete_response = admin_client.delete(f"/api/v1/users/{user_id}")
        assert delete_response.status_code == 204

    def test_user_crud_operations_non_admin(self, authenticated_client: TestClient):
        """Test CRUD operations for users as non-admin."""
        # All CRUD operations should be forbidden for non-admin users
        user_data = {
            "name": "New Test User",
            "email": "<EMAIL>",
            "password": "NewUserPass123",
            "role": UserRole.VIEWER.value,
            "is_active": True,
        }

        create_response = authenticated_client.post("/api/v1/users/", json=user_data)
        assert create_response.status_code == 403

    def test_user_list_pagination_admin(self, admin_client: TestClient):
        """Test user list pagination as admin."""
        response = admin_client.get("/api/v1/users/?page=1&page_size=10")

        assert response.status_code == 200
        data = response.json()

        # Should return paginated response structure
        assert "items" in data
        assert "pagination" in data

        # Check pagination object structure
        pagination = data["pagination"]
        expected_pagination_fields = ["page", "size", "total", "pages"]
        for field in expected_pagination_fields:
            assert field in pagination

    def test_user_validation_errors(self, admin_client: TestClient):
        """Test user creation with validation errors."""
        # Missing required fields
        invalid_user_data = {
            "name": "Test User"
            # Missing email, password, etc.
        }

        response = admin_client.post(
            "/api/v1/users/", json=invalid_user_data
        )
        assert response.status_code == 422

    def test_user_endpoints_performance_monitoring(
        self, authenticated_client: TestClient
    ):
        """Test that user endpoints include performance monitoring."""
        # Performance monitoring should be transparent to the client
        response = authenticated_client.get("/api/v1/users/me")

        assert response.status_code == 200
        # Performance metrics are logged, not returned in response
