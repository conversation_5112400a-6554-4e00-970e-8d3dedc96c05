# tests/unit/test_errors/test_error_context.py
"""
Comprehensive tests for unified error handling system.

This module tests the unified error handler including error contexts.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
from contextlib import contextmanager

import pytest
from fastapi import Request, HTTPException

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    ErrorContext,
    ErrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    get_unified_error_handler,
    handle_service_errors,
    handle_repository_errors,
    handle_calculation_errors,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    NotFoundError,
    DataValidationError,
    InvalidInputError,
    ServiceError,
    DatabaseError,
    CalculationError,
    DuplicateEntryError,
)

pytestmark = [pytest.mark.unit]


class TestErrorContext:
    """Test suite for ErrorContext enum."""

    def test_error_context_values(self):
        """Test ErrorContext enum values."""
        assert ErrorContext.API.value == "API"
        assert ErrorContext.SERVICE.value == "Service Layer"
        assert ErrorContext.REPOSITORY.value == "Repository"
        assert ErrorContext.CALCULATION.value == "Calculation"
        assert ErrorContext.MIDDLEWARE.value == "Middleware"
        assert ErrorContext.VALIDATION.value == "Validation"
        assert ErrorContext.DATABASE.value == "Database"
        assert ErrorContext.SECURITY.value == "Security"

    def test_error_context_iteration(self):
        """Test that ErrorContext can be iterated."""
        contexts = list(ErrorContext)
        assert len(contexts) >= 8
        assert ErrorContext.API in contexts
        assert ErrorContext.SERVICE in contexts
        assert ErrorContext.REPOSITORY in contexts
        assert ErrorContext.CALCULATION in contexts
        assert ErrorContext.MIDDLEWARE in contexts
        assert ErrorContext.VALIDATION in contexts
        assert ErrorContext.DATABASE in contexts
        assert ErrorContext.SECURITY in contexts
