# Project Brief: Ultimate Electrical Designer

## 1. Project Overview

The Ultimate Electrical Designer is an engineering-grade platform for professional electrical system design. It provides a comprehensive suite of tools for electrical engineering tasks, including heat tracing design, power distribution, standards compliance validation, and professional report generation. The platform is built to meet the highest standards of quality, reliability, and precision, with an immaculate attention to detail.

## 2. Core Features

- **Heat Tracing Design**: End-to-end thermal analysis, automated cable selection (self-regulating and series resistance), power requirement calculations, and compliance with IEC/EN thermal standards.
- **Electrical System Design**: Complete electrical system design, optimized cable routing, professional switchboard layouts, and comprehensive load calculations.
- **Component Management**: An extensive and hierarchical database of electrical components across 13 professional categories, with specifications mapped to relevant industry standards.
- **Standards Validation**: Automated compliance checking against key international standards, including IEC (60079, 61508, 60364, 60287), EN (50110, 60204, 50522), and various IEEE standards.
- **Report Generation**: Professional documentation and calculation reports.

## 3. System Architecture

The project is built on a microservices-oriented architecture:

- **Backend (`server/`)**: A Python backend using the FastAPI framework, responsible for core business logic, API endpoints, and database management. It follows a strict 5-layer architecture.
- **Frontend (`client/`)**: A Next.js client application (structure prepared) for the user interface and experience.
- **CAD Integrator Service (`cad-integrator-service/`)**: A C#/.NET microservice for integration with AutoCAD.
- **Computation Engine Service (`computation-engine-service/`)**: A C#/.NET microservice for high-performance electrical calculations and simulations.

## 4. Key Technologies

- **Backend**: Python 3.13+, FastAPI, SQLAlchemy, PostgreSQL/SQLite, NumPy/SciPy/Pandas.
- **Frontend**: Next.js 15.3+, TypeScript, Tailwind CSS, React Query, Zustand.
- **Integration Services**: C# .NET 8.0+, ASP.NET Core, Docker.
- **Testing**: Pytest (backend), Vitest/React Testing Library/Playwright (frontend).

## 5. Project Goals & Vision

The vision is to create a definitive, all-in-one electrical design platform that combines engineering precision with a seamless user experience. The project emphasizes:
- **Engineering-Grade Quality**: Zero tolerance for warnings, errors, or technical debt.
- **Professional Standards**: Strict adherence to international electrical and safety standards.
- **Scalability & Maintainability**: A clean, well-documented, and scalable architecture.
- **Automation**: Leveraging AI agents for code quality, testing, and documentation.
