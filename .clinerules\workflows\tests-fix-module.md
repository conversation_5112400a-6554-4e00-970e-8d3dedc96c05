### 11. Workflow: Fixing Failing Tests for a Specific Module

This workflow provides a more granular approach to resolving test failures, focusing on a particular backend or frontend module.

* **11.1. Discovery & Analysis:**
    * **Task:** Identify the failing tests specifically within the designated module (backend Python or frontend TypeScript/React) and determine their root causes.
    * **Guidance for AI:** Access the test report filtered by module (e.g., `pytest tests/path/to/backend_module/` or `npm run test src/modules/frontend_module/`). Analyze logs and code related to the module's failures.
* **11.2. Task Planning:**
    * **Task:** Plan the fix within the module's context, focusing on isolated changes to resolve the specific test failures.
    * **Guidance for AI:** Create a targeted plan for code modifications and testing within the module, considering its specific technology stack (Python/FastAPI or TypeScript/React).
* **11.3. Implementation:**
    * **Task:** Implement code changes specific to the module to resolve the test failures. Ensure adherence to the module's existing design and "unified patterns" (backend) or **Component-Based Architecture** (frontend).
    * **Guidance for AI:** Generate precise code modifications for the module. Ensure no new warnings or errors are introduced by linting/type-checking tools specific to the module's language.
* **11.4. Verification:**
    * **Task:** Rerun the module-specific tests and relevant integration tests to ensure the fixes are effective and no regressions are introduced within the module or its immediate dependencies.
    * **Guidance for AI:** Execute the specific module's test suite (e.g., `pytest` for backend, `vitest` or `playwright` for frontend). Confirm "100% test pass rates" for the module.
* **11.5. Documentation & Handover:**
    * **Task:** Document the fix for the module, including the specific tests that were failing and how they were resolved.
    * **Guidance for AI:** Update the module's documentation or related issue entries with details of the fix.