### 10. Workflow: Fixing All Failing Tests

This workflow defines the procedure for addressing and resolving all identified test failures across the entire project, ensuring "100% test pass rates" for both backend and frontend tests.

* **10.1. Discovery & Analysis:**
    * **Task:** Isolate all failing tests and identify the root cause of each failure across backend (Python) and frontend (TypeScript) tests. This may involve reviewing recent code changes, build logs, and test reports.
    * **Guidance for AI:** Access and analyze the full test report from all test runners (e.g., **pytest**, **Vitest**, **Playwright**). Correlate failing tests with recent commits or known issues across the entire codebase.
* **10.2. Task Planning:**
    * **Task:** Prioritize the fixes, starting with critical or foundational failures. Plan the necessary code changes to resolve each identified test failure, specifying whether it's a backend or frontend fix.
    * **Guidance for AI:** Create a prioritized list of failing tests and propose a remediation plan, ideally grouping related fixes by domain (backend/frontend).
* **10.3. Implementation:**
    * **Task:** Implement the necessary code changes to resolve the test failures in the respective backend or frontend codebases. Ensure fixes adhere to "Development Standards" and do not introduce new issues.
    * **Guidance for AI:** Generate targeted code modifications for the specific failing tests. Apply "Zero Tolerance Policies" for any new warnings, errors, or technical debt introduced by the fix in either environment.
* **10.4. Verification:**
    * **Task:** Rerun *all* project tests (backend and frontend) to ensure comprehensive test pass rates are achieved and no new regressions have been introduced.
    * **Guidance for AI:** Execute the entire test suite (**`poetry run pytest`** for backend, **`npm run test`** and **`npx playwright test`** for frontend). Confirm that all tests now pass and the project achieves "100% test pass rates". Use the "Quality Assurance Checklist" to verify overall quality.
* **10.5. Documentation & Handover:**
    * **Task:** Document the root cause of the failures, the implemented fix, and its impact.
    * **Guidance for AI:** Update relevant issue trackers (GitHub Issues) and internal documentation to detail the resolution, specifying whether the fix was backend or frontend related.