### 8. Workflow: Implementing a New Feature

This workflow guides the AI Agent through the end-to-end process of developing a completely new feature, involving both backend and frontend components.

* **8.1. Discovery & Analysis:**
    * **Task:** Gain a comprehensive understanding of the new feature's requirements, user stories, and its overall impact on the system. Define clear objectives and scope for both backend and frontend parts.
    * **Guidance for AI:** Analyze user stories and functional specifications. Map the feature's components to the appropriate layers of the **5-layer architecture** (backend) and **Component-Based Architecture** (frontend). Identify necessary data models, API endpoints, core logic, UI components, and state management.
* **8.2. Task Planning:**
    * **Task:** Break down the feature into epics and smaller, manageable stories, following the "5-Phase Methodology" for each sub-task. Utilize the "Task Planning Template".
    * **Guidance for AI:** Generate a detailed project plan for the feature, including dependencies between backend and frontend sub-tasks and estimated timelines. Ensure all sub-tasks align with project standards (e.g., TDD, Unified Patterns, Atomic Design).
* **8.3. Implementation:**
    * **Task:** Develop the feature's components across all relevant layers (API, core logic, repositories, frontend, CAD integration), ensuring strict adherence to "SOLID" principles, "Unified Patterns", "complete type safety" (MyPy validation for Python, TypeScript for frontend), and "engineering-grade quality".
    * **Guidance for AI:** Generate code for backend services (**FastAPI, SQLAlchemy, NumPy, SciPy, Pandas**), frontend components (**Next.js, TypeScript, React Query, Zustand, Tailwind CSS, shadcn/ui**), and any necessary CAD integration (AutoCAD .NET API). Apply decorators for error handling, performance, and memory optimization. Ensure "Zero Tolerance Policies" for warnings/errors across the entire stack.
* **8.4. Verification:**
    * **Task:** Conduct comprehensive testing for the new feature, including unit, integration, performance, and end-to-end tests across both backend and frontend. Aim for 100% code coverage for all new implementations. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate and execute test cases (**pytest** for backend, **Vitest/React Testing Library/Playwright** for frontend). Verify "100% test pass rates" and analyze code coverage reports to ensure targets are met. Conduct compliance verification against relevant IEEE/IEC/EN standards. Perform **Core Web Vitals** performance audits for the frontend.
* **8.5. Documentation & Handover:**
    * **Task:** Create comprehensive feature documentation, including API specifications, user guides, frontend component documentation, and any relevant calculation reports. Prepare a complete handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update the "Developer Handbook", "Frontend Specification", and "Professional Documentation" sections. Ensure "engineering-grade precision" and clarity across all documentation.