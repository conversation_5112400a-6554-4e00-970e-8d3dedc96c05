# tests/api/test_health_routes.py
"""Tests for health check API endpoints."""

from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock
from datetime import datetime

from src.core.services.health_service import HealthService


class TestHealthRoutes:
    """Test suite for health check endpoints."""

    def test_simple_health_check_healthy(self, client: TestClient):
        """Test simple health check returns healthy status."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {"connection_responsive": True}
            response = client.get("/api/v1/health/")

        assert response.status_code == 200
        data = response.json()

        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "database_status" in data
        assert "uptime_seconds" in data

        # Verify timestamp format
        timestamp = datetime.fromisoformat(data["timestamp"].replace("Z", "+00:00"))
        assert isinstance(timestamp, datetime)

    def test_simple_health_check_unhealthy_database(self, client: TestClient):
        """Test simple health check with unhealthy database."""
        from src.core.schemas.health import SimpleHealthResponseSchema
        with patch("src.core.services.health_service.HealthService") as mock_health_service:
            mock_health_service.return_value.get_simple_health.return_value = SimpleHealthResponseSchema(
                status="unhealthy",
                database_status="unhealthy",
                version="1.0.0",
                timestamp=datetime.now(),
                uptime_seconds=123,
            )
            response = client.get("/api/v1/health/")
            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "unhealthy"
            assert data["database_status"] == "unhealthy"

    def test_comprehensive_health_check_healthy(self, client: TestClient):
        """Test comprehensive health check returns detailed status."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {
                "connection_responsive": True,
                "connection_latency_ms": 10.0,
                "pool_utilization": 0.5,
                "pool_metrics": {},
                "health_score": 10,
                "recommendations": [],
                "database_type": "sqlite",
                "connection_error": None,
            }
            response = client.get("/api/v1/health/detailed")

        assert response.status_code == 200
        data = response.json()

        # Verify required fields
        required_fields = [
            "status",
            "timestamp",
            "version",
            "environment",
            "uptime_seconds",
            "database",
            "services",
            "health_score",
            "critical_issues",
            "warnings",
            "checks_performed",
        ]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"

        # Verify database section
        db_data = data["database"]
        db_required_fields = [
            "status",
            "connection_responsive",
            "pool_utilization",
            "health_score",
            "database_type",
        ]
        for field in db_required_fields:
            assert field in db_data, f"Missing database field: {field}"

        # Verify services section
        assert isinstance(data["services"], list)
        if data["services"]:
            service = data["services"][0]
            service_required_fields = ["name", "status", "last_check"]
            for field in service_required_fields:
                assert field in service, f"Missing service field: {field}"

    def test_comprehensive_health_check_unhealthy(self, client: TestClient):
        """Test comprehensive health check with unhealthy system."""
        from src.core.schemas.health import HealthCheckResponseSchema, DatabaseHealthSchema
        with patch("src.core.services.health_service.HealthService") as mock_health_service:
            mock_health_service.return_value.get_comprehensive_health.return_value = HealthCheckResponseSchema(
                status="unhealthy",
                health_score=0,
                critical_issues=["Database connection failed"],
                warnings=[],
                database=DatabaseHealthSchema(
                    status="unhealthy",
                    connection_responsive=False,
                    connection_latency_ms=None,
                    pool_utilization=0.0,
                    pool_metrics={},
                    health_score=0,
                    recommendations=["Database connection failed"],
                    database_type="sqlite",
                    connection_error="Connection timeout",
                ),
                services=[],
                system_metrics=None,
                version="1.0.0",
                timestamp=datetime.now(),
                uptime_seconds=123,
                checks_performed=[],
                response_time_ms=1,
                environment="testing",
            )
            response = client.get("/api/v1/health/detailed")
            assert response.status_code == 503
            data = response.json()
            assert data["status"] == "unhealthy"
            assert len(data["critical_issues"]) > 0

    def test_ping_endpoint(self, client: TestClient):
        """Test ping endpoint for basic connectivity."""
        response = client.get("/api/v1/health/ping")

        assert response.status_code == 200
        data = response.json()

        assert data["status"] == "pong"
        assert "timestamp" in data

        # Verify timestamp format
        timestamp = datetime.fromisoformat(data["timestamp"].replace("Z", "+00:00"))
        assert isinstance(timestamp, datetime)

    def test_readiness_check_ready(self, client: TestClient):
        """Test readiness check when system is ready."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {"connection_responsive": True}
            response = client.get("/api/v1/health/ready")

        assert response.status_code == 200
        data = response.json()

        assert data["ready"] is True
        assert "database_status" in data
        assert "timestamp" in data

    def test_readiness_check_not_ready(self, client: TestClient):
        """Test readiness check when system is not ready."""
        from src.core.schemas.health import SimpleHealthResponseSchema
        with patch("src.core.services.health_service.HealthService") as mock_health_service:
            mock_health_service.return_value.get_simple_health.return_value = SimpleHealthResponseSchema(
                status="unhealthy",
                database_status="unhealthy",
                version="1.0.0",
                timestamp=datetime.now(),
                uptime_seconds=123,
            )
            response = client.get("/api/v1/health/ready")
            assert response.status_code == 503
            data = response.json()
            assert data["ready"] is False

    def test_liveness_check(self, client: TestClient):
        """Test liveness check endpoint."""
        response = client.get("/api/v1/health/live")

        assert response.status_code == 200
        data = response.json()

        assert data["alive"] is True
        assert "timestamp" in data

    def test_health_service_performance_monitoring(self, client: TestClient):
        """Test that health endpoints include performance monitoring."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {
                "connection_responsive": True,
                "connection_latency_ms": 10.0,
                "pool_utilization": 0.5,
                "pool_metrics": {},
                "health_score": 10,
                "recommendations": [],
                "database_type": "sqlite",
                "connection_error": None,
            }
            response = client.get("/api/v1/health/detailed")

        assert response.status_code == 200
        data = response.json()

        # Verify response time is tracked
        assert "response_time_ms" in data
        assert isinstance(data["response_time_ms"], (int, float))
        assert data["response_time_ms"] > 0

    def test_health_service_error_handling(self, client: TestClient):
        """Test health service error handling."""
        import pytest
        from fastapi import HTTPException

        with patch(
            "src.core.services.health_service.HealthService.get_simple_health",
            side_effect=Exception("Unexpected error"),
        ):
            with pytest.raises(HTTPException) as exc_info:
                client.get("/api/v1/health/")
            assert exc_info.value.status_code == 500

    def test_health_check_system_metrics(self, client: TestClient):
        """Test system metrics collection in health check."""
        with (
            patch("psutil.virtual_memory") as mock_memory,
            patch("psutil.cpu_percent") as mock_cpu,
            patch("psutil.disk_usage") as mock_disk,
            patch("src.core.services.health_service.monitor_connection_health") as mock_db_health,
        ):
            mock_db_health.return_value = {
                "connection_responsive": True,
                "connection_latency_ms": 10.0,
                "pool_utilization": 0.5,
                "pool_metrics": {},
                "health_score": 10,
                "recommendations": [],
                "database_type": "sqlite",
                "connection_error": None,
            }
            # Mock system metrics
            mock_memory.return_value = MagicMock(used=500 * 1024 * 1024)  # 500MB
            mock_cpu.return_value = 25.5  # 25.5% CPU
            mock_disk.return_value = MagicMock(
                used=50 * 1024**3, total=100 * 1024**3
            )  # 50% disk

            response = client.get("/api/v1/health/detailed")

            assert response.status_code == 200
            data = response.json()

            if data.get("system_metrics"):
                metrics = data["system_metrics"]
                assert "memory_usage_mb" in metrics
                assert "cpu_usage_percent" in metrics
                assert "disk_usage_percent" in metrics

    def test_health_check_database_metrics(self, client: TestClient):
        """Test database metrics in health check."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {
                "connection_responsive": True,
                "connection_latency_ms": 10.0,
                "pool_utilization": 0.5,
                "pool_metrics": {},
                "health_score": 10,
                "recommendations": [],
                "database_type": "sqlite",
                "connection_error": None,
            }
            response = client.get("/api/v1/health/detailed")

        assert response.status_code == 200
        data = response.json()

        db_data = data["database"]
        assert "pool_utilization" in db_data
        assert "health_score" in db_data
        assert isinstance(db_data["health_score"], int)
        assert 0 <= db_data["health_score"] <= 10

    def test_health_check_recommendations(self, client: TestClient):
        """Test health check recommendations."""
        with patch(
            "src.core.services.health_service.monitor_connection_health"
        ) as mock_health:
            mock_health.return_value = {
                "connection_responsive": True,
                "health_score": 7,
                "pool_utilization": 0.85,  # High utilization
                "pool_metrics": {},
                "recommendations": ["Consider increasing connection pool size"],
                "database_type": "sqlite",
                "connection_error": None,
            }

            response = client.get("/api/v1/health/detailed")

            assert response.status_code == 200
            data = response.json()

            db_data = data["database"]
            assert "recommendations" in db_data
            assert isinstance(db_data["recommendations"], list)
            if db_data["recommendations"]:
                assert isinstance(db_data["recommendations"][0], str)

    def test_health_check_content_type(self, client: TestClient):
        """Test health check response content type."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {"connection_responsive": True}
            response = client.get("/api/v1/health/")

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

    def test_health_check_cors_headers(self, client: TestClient):
        """Test health check CORS headers if applicable."""
        with patch("src.core.services.health_service.monitor_connection_health") as mock_db_health:
            mock_db_health.return_value = {"connection_responsive": True}
            response = client.get("/api/v1/health/")

        assert response.status_code == 200
        # Health endpoints should be accessible for monitoring
        # CORS headers would be added by middleware if configured
