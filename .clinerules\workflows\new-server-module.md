### 1. Workflow: Implementing a New Module

This workflow guides the AI Agent through the process of creating and integrating a completely new module into the system, aligning with the "Core API Implementation & Foundation Setup" phase.

* **1.1. Discovery & Analysis:**
    * **Task:** Identify the new module's purpose, define its clear scope, and analyze its required interactions and dependencies within the existing 5-layer architecture.
    * **Guidance for AI:** Review existing architectural diagrams and identify the appropriate layer for the new module (e.g., `src/api/`, `src/core/`, `src/repositories/`). Verify no existing functionality overlaps.
* **1.2. Task Planning:**
    * **Task:** Break down the module implementation into smaller, manageable sub-tasks using the "Task Planning Template". This includes defining data models (SQLAlchemy ORM models), API endpoints (FastAPI routes), core business logic, and repository interfaces.
    * **Guidance for AI:** Generate a detailed task list, estimating complexity and dependencies. Ensure adherence to "Unified Patterns" for proposed new components.
* **1.3. Implementation:**
    * **Task:** Develop the module's components according to the planned tasks, strictly adhering to "SOLID" principles, "Unified Patterns", and achieving full type safety (MyPy validation for Python).
    * **Guidance for AI:** Generate code for models, schemas, API routes, core logic, and repositories. Apply decorators for error handling, performance monitoring, and memory optimization as per "Unified Patterns". Ensure "Zero Tolerance Policies" for warnings/errors are met.
* **1.4. Verification:**
    * **Task:** Conduct comprehensive testing, including unit, integration, and performance tests, with a target of 100% code coverage for the newly implemented module. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate test cases (using Pytest for backend). Execute tests and verify "100% test pass rates". Analyze coverage reports and suggest additional tests if coverage is below 100% for the new code.
* **1.5. Documentation & Handover:**
    * **Task:** Update the "Developer Handbook" with details of the new module and prepare a comprehensive handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update relevant sections in the developer handbook, including API specifications. Ensure all public APIs are documented.