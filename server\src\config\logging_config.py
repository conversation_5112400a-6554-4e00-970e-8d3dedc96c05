# src/config/logging_config.py
"""Logging Configuration"""

import logging  # For intercepting standard logging
import os
import sys
from types import FrameType
from typing import Any, Dict, Union

from loguru import logger

# Explicit exports for MyPy
__all__ = ["logger", "setup_logging", "InterceptHandler"]


def setup_logging(debug_mode: bool = False, json_logs: bool = False) -> None:
    """
    Sets up Loguru for unified logging across the application.

    Args:
        debug_mode (bool): If True, sets console log level to DEBUG and enables diagnose/backtrace.
                           Otherwise, sets to INFO.
        json_logs (bool): If True, outputs logs in JSON format for easier machine parsing.
    """
    # 1. Remove default handler
    # Loguru by default adds a handler to stderr. We remove it to take full control.
    logger.remove()

    # 2. Configure console logging (stderr)
    console_level = "DEBUG" if debug_mode else "INFO"
    log_format_console = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    if json_logs:
        # If JSON logs are primarily for file/external systems, console might still be text.
        # Or, you can disable colorization for a raw JSON output on console if that's desired.
        log_format_console = (
            "{message}"  # When serialize=True, message is the JSON string
        )

    logger.add(
        sys.stderr,
        level=console_level,
        format=log_format_console,
        colorize=not json_logs,  # Colorize only if not JSON
        backtrace=debug_mode,  # Show full tracebacks in debug mode
        diagnose=debug_mode,  # Show variable values in debug mode
        enqueue=True,  # Recommended for multi-process/multi-thread applications
    )

    # 3. Configure file logging for general application logs
    log_file_path = os.getenv("LOG_FILE_PATH", "logs/application.log")
    log_level_file = os.getenv("LOG_FILE_LEVEL", "INFO")  # Default to INFO for file

    # Ensure logs directory exists
    os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

    file_sink_kwargs: Dict[str, Any] = {
        "rotation": os.getenv(
            "LOG_ROTATION", "500 MB"
        ),  # Rotate when file size reaches 500 MB
        "retention": os.getenv("LOG_RETENTION", "10 days"),  # Keep logs for 10 days
        "compression": os.getenv("LOG_COMPRESSION", "zip"),  # Compress old logs
        "level": log_level_file,
        "enqueue": True,  # CRITICAL for multi-process environments (e.g., Gunicorn/Uvicorn workers)
        "backtrace": debug_mode,
        "diagnose": debug_mode,
    }

    if json_logs:
        file_sink_kwargs["serialize"] = True  # Output logs as JSON lines
        file_sink_kwargs["format"] = (
            "{message}"  # Loguru handles JSON formatting when serialize is True
        )
    else:
        file_sink_kwargs["format"] = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
            "{process.name}:{thread.name} | {name}:{function}:{line} - {message}"
        )

    logger.add(log_file_path, **file_sink_kwargs)

    # 4. Configure a separate error log file (optional but highly recommended for critical errors)
    error_log_file_path = os.getenv("ERROR_LOG_FILE_PATH", "logs/error.log")
    os.makedirs(os.path.dirname(error_log_file_path), exist_ok=True)
    logger.add(
        error_log_file_path,
        level="ERROR",  # Only log ERROR and CRITICAL messages to this file
        rotation=os.getenv("ERROR_LOG_ROTATION", "10 MB"),
        retention=os.getenv("ERROR_LOG_RETENTION", "30 days"),
        compression=os.getenv("ERROR_LOG_COMPRESSION", "zip"),
        enqueue=True,
        backtrace=True,  # Always capture full tracebacks for errors
        diagnose=True,  # Always capture variable values for errors
        serialize=json_logs,
        format=(
            "{message}"
            if json_logs
            else (
                "<red>{time:YYYY-MM-DD HH:mm:ss.SSS}</red> | "
                "<level>{level: <8}</level> | "
                "<yellow>{process.name}:{thread.name}</yellow> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <red>{message}</red>"
            )
        ),
    )

    # 5. Intercept standard Python logging (CRUCIAL for unified logging)
    # This redirects all messages from Python's built-in `logging` module to Loguru.
    class InterceptHandler(logging.Handler):
        def emit(self, record: logging.LogRecord) -> None:
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = str(record.levelno)

            # Find the caller from where the log message originated
            frame: Union[FrameType, None] = logging.currentframe()
            depth = 2
            while (
                frame and frame.f_code.co_filename == logging.__file__
            ):  # Skip internal logging frames
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )

    # Apply the InterceptHandler to the root logger
    # `force=True` is important for Python 3.8+ to ensure handlers are replaced.
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # Optional: Configure specific external loggers to prevent duplication or too much verbosity.
    # Set their handlers to an empty list and manage propagation.
    # For Uvicorn (FastAPI's server), you often want its access logs.
    # Set levels to control what gets through.

    # Uvicorn's main logger (info about server startup/shutdown)
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.handlers = []
    uvicorn_logger.setLevel(logging.INFO)
    uvicorn_logger.propagate = True  # Allow it to go to our InterceptHandler

    # Uvicorn's access logger (HTTP requests)
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.handlers = []
    uvicorn_access_logger.setLevel(logging.INFO)  # Keep access logs as INFO
    uvicorn_access_logger.propagate = True

    # Example for other potentially noisy libraries:
    # logging.getLogger("sqlalchemy.engine").propagate = False # Disable SQLAlchemy query logging
    # logging.getLogger("httpx").propagate = False # Disable HTTPX client logging
    # logging.getLogger("some_noisy_library").setLevel(logging.WARNING) # Or even CRITICAL

    logger.info("Logging system configured and ready.")
