# System Patterns: Ultimate Electrical Designer

## 1. High-Level Architecture: Microservices

The Ultimate Electrical Designer employs a microservices-oriented architecture to ensure separation of concerns, scalability, and maintainability. This approach allows for independent development, deployment, and scaling of different parts of the system.

The primary services are:
- **Backend Service (`server/`)**: The central hub for business logic, data processing, and API provision. Built with Python and FastAPI.
- **Frontend Service (`client/`)**: The user-facing application, responsible for all UI and client-side state management. Built with Next.js.
- **CAD Integrator Service (`cad-integrator-service/`)**: A specialized C# service to handle integrations with AutoCAD, isolating CAD-specific dependencies and logic.
- **Computation Engine Service (`computation-engine-service/`)**: A dedicated C# service for handling intensive, performance-critical electrical engineering calculations.

## 2. Backend Architecture: 5-Layer Pattern

The Python backend strictly adheres to a **5-Layer Architecture** to ensure a clean separation of concerns. This pattern is fundamental to the backend's design and must be followed for all new feature development.

The layers are organized as follows:
1.  **API Layer (`src/api/`)**: Exposes endpoints to the outside world (e.g., the frontend). This layer is responsible for request handling and response formatting. It should contain minimal business logic, delegating tasks to the service layer.
2.  **Service Layer (`src/core/services/`)**: *Note: The `README.md` implies services are part of the core logic, this is where they would reside.* This layer orchestrates business logic, calling upon repositories and other core components to fulfill requests.
3.  **Repository Layer (`src/core/repositories/`)**: Manages all data access operations. It abstracts the database, providing a clean API for the service layer to interact with data without knowing the underlying database implementation (e.g., SQLAlchemy).
4.  **Model Layer (`src/core/models/`)**: Defines the application's data structures, primarily through SQLAlchemy ORM models. These represent the database tables.
5.  **Schema Layer (`src/core/schemas/`)**: Contains Pydantic schemas for data validation, serialization, and deserialization at the API boundaries. This ensures data integrity and provides clear data contracts.

## 3. Frontend Architecture: Component-Based & Atomic Design

The frontend (when implemented) will follow a modern, component-based architecture based on **Atomic Design principles**. This promotes reusability, consistency, and a scalable design system.

- **Component Hierarchy**: Components will be organized from smallest to largest:
    - **Atoms (`src/components/ui/`)**: The smallest, indivisible UI elements (e.g., buttons, inputs, labels).
    - **Molecules (`src/components/common/`)**: Simple combinations of atoms that form a functional unit (e.g., a search bar with a button).
    - **Organisms (`src/components/domain/`)**: More complex UI components composed of molecules and/or atoms to form distinct sections of an interface.
    - **Templates/Pages (`src/app/`)**: The final layout structures that arrange organisms into complete pages.
- **Domain Modules (`src/modules/`)**: To keep the application organized, domain-specific logic, components, hooks, and types will be co-located within feature-based modules (e.g., `modules/heat_tracing/`).

## 4. Cross-Cutting Concerns: Unified Patterns

A key principle of the architecture is the use of "unified patterns" to handle cross-cutting concerns consistently across the application.

- **Error Handling (`src/core/errors/`)**: A centralized system for defining custom exceptions and handling errors gracefully. Decorators are used to standardize error responses across the API.
- **Security (`src/core/security/`)**: Unified validation and authentication logic, primarily using JWT.
- **Monitoring (`src/core/monitoring/`)**: Standardized performance monitoring, likely using decorators or middleware to track request latency and other metrics.
- **Data Flow (Frontend)**: A strict unidirectional data flow will be enforced:
    - **Server State**: Managed exclusively by **React Query (TanStack Query)** for data fetching, caching, and synchronization.
    - **Client State**: Managed with **Zustand** for global state and `useState`/`useReducer` for local component state.

## 5. Development Methodology: 5-Phase Approach

All development work, from small features to large-scale changes, must follow a structured 5-phase methodology:
1.  **Discovery & Analysis**: Understand requirements and system state.
2.  **Task Planning**: Break down the work into manageable units.
3.  **Implementation**: Write code that adheres to all architectural patterns and quality standards.
4.  **Verification**: Ensure requirements are met via comprehensive testing.
5.  **Documentation & Handover**: Update all relevant documentation.
