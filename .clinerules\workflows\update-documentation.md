### 12. Workflow: Update Development Documentation

This workflow outlines the process for maintaining and updating the project's development documentation, ensuring "Comprehensive documentation" and contributing to "Documentation Coverage" across both backend and frontend. This includes resources like the Developer Handbook, Backend Development, Frontend Specification, etc.

* **12.1. Discovery & Analysis:**
    * **Task:** Identify outdated, incomplete, or missing sections within the development documentation. This can be triggered by new features, bug fixes, refactoring, or a review of the "Documentation Coverage" metric for both backend and frontend.
    * **Guidance for AI:** Review recent code changes, new feature implementations, and project discussions to pinpoint areas where documentation updates are necessary. Refer to the "Developer Handbook", "Backend Development", and "Frontend Specification" to understand existing structure.
* **12.2. Task Planning:**
    * **Task:** Plan the scope of the documentation update, outlining specific sections or documents that require modification or creation for either backend or frontend.
    * **Guidance for AI:** Create a detailed plan for the documentation update, specifying the content to be added, modified, or removed, and cross-referencing with relevant code changes in both Python and TypeScript.
* **12.3. Implementation:**
    * **Task:** Write or update the documentation, ensuring it is accurate, clear, and adheres to the project's standard for "engineering-grade precision". This includes updating API documentation, component guides, architectural explanations, and any domain-specific documentation.
    * **Guidance for AI:** Generate or modify markdown files (`.md`) within the `docs/` directory. Ensure all technical details are correct, consistent with the code, and easy to understand for other developers and future AI agents. Maintain "100% type coverage" for code examples within documentation. Use **JSDoc/TypeDoc** for inline frontend documentation.
* **12.4. Verification:**
    * **Task:** Review the updated documentation for accuracy, clarity, completeness, and adherence to documentation standards. This may involve a peer review or automated checks for broken links.
    * **Guidance for AI:** Conduct a self-review of the generated documentation. Cross-reference against the actual codebase (backend and frontend) and functional specifications to ensure consistency.
* **12.5. Documentation & Handover:**
    * **Task:** Publish the updated documentation and ensure it is accessible to the development team and other AI agents.
    * **Guidance for AI:** Commit the changes to the documentation repository. Ensure the updated documentation is part of the handover package for relevant features.